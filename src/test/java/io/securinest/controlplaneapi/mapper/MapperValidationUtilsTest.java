package io.securinest.controlplaneapi.mapper;

import static org.junit.jupiter.api.Assertions.*;

import io.securinest.controlplaneapi.entity.shared.PolicyState;
import java.util.UUID;
import org.junit.jupiter.api.Test;

class MapperValidationUtilsTest {

  @Test
  void testToStringOrNull_withValidObject() {
    String input = "test string";
    String result = MapperValidationUtils.toStringOrNull(input);
    assertEquals("test string", result);
  }

  @Test
  void testToStringOrNull_withNull() {
    String result = MapperValidationUtils.toStringOrNull(null);
    assertNull(result);
  }

  @Test
  void testToStringOrNull_withUUID() {
    UUID uuid = UUID.randomUUID();
    String result = MapperValidationUtils.toStringOrNull(uuid);
    assertEquals(uuid.toString(), result);
  }

  @Test
  void testEnumNameOrNull_withValidEnum() {
    PolicyState state = PolicyState.ACTIVE;
    String result = MapperValidationUtils.enumNameOrNull(state);
    assertEquals("ACTIVE", result);
  }

  @Test
  void testEnumNameOrNull_withNull() {
    String result = MapperValidationUtils.enumNameOrNull(null);
    assertNull(result);
  }

  @Test
  void testUuidToStringOrNull_withValidUUID() {
    UUID uuid = UUID.randomUUID();
    String result = MapperValidationUtils.uuidToStringOrNull(uuid);
    assertEquals(uuid.toString(), result);
  }

  @Test
  void testUuidToStringOrNull_withNull() {
    String result = MapperValidationUtils.uuidToStringOrNull(null);
    assertNull(result);
  }
}
