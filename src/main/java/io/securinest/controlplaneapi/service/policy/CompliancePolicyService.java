package io.securinest.controlplaneapi.service.policy;

import io.securinest.controlplaneapi.dto.policy.CompliancePolicyCreateRequest;
import io.securinest.controlplaneapi.dto.policy.CompliancePolicyResponse;
import io.securinest.controlplaneapi.dto.policy.CompliancePolicyUpdateRequest;
import io.securinest.controlplaneapi.entity.policy.CompliancePolicy;
import io.securinest.controlplaneapi.entity.shared.PolicyState;
import io.securinest.controlplaneapi.exceptions.shared.SecurinestException;
import io.securinest.controlplaneapi.mapper.policy.CompliancePolicyMapper;
import io.securinest.controlplaneapi.repository.policy.CompliancePolicyRepository;
import io.securinest.controlplaneapi.util.shared.PageableValidationUtils;
import io.securinest.controlplaneapi.util.shared.ValidationUtils;
import java.util.Objects;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.orm.ObjectOptimisticLockingFailureException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ResponseStatusException;

@Service
@RequiredArgsConstructor
@Slf4j
public class CompliancePolicyService {

  private final CompliancePolicyRepository policyRepository;
  private final CompliancePolicyMapper policyMapper;

  @Transactional
  public CompliancePolicyResponse create(UUID tenantId, CompliancePolicyCreateRequest request,
      UUID requesterId, String requestId) {
    if (tenantId == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing tenant ID");
    }
    if (request == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing body");
    }
    if (requesterId == null) {
      throw new SecurinestException(HttpStatus.UNAUTHORIZED, "Missing current user");
    }

    log.debug("Creating compliance policy for tenant: {}", tenantId);

    // Validate and normalize name
    String normalizedName = ValidationUtils.trimToNull(request.name());
    if (normalizedName == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Policy name is required");
    }

    // Check name uniqueness
    if (policyRepository.existsByTenantIdAndNameIgnoreCase(tenantId, normalizedName)) {
      throw new SecurinestException(HttpStatus.CONFLICT,
          "Policy name already exists: " + normalizedName);
    }

    CompliancePolicy policy = policyMapper.fromRequest(request);
    policy.setTenantId(tenantId);
    policy.setName(normalizedName);
    policy.setCreatedBy(requesterId);

    if (StringUtils.hasText(request.description())) {
      policy.setDescription(ValidationUtils.trimToNull(request.description()));
    }

    try {
      CompliancePolicy saved = policyRepository.save(policy);
      log.info("Created compliance policy: {} for tenant: {}", saved.getId(), tenantId);
      return policyMapper.toResponse(saved);
    } catch (DataIntegrityViolationException e) {
      log.warn("createPolicy constraint issue. requestId={} msg={}", requestId, e.getMessage());
      throw new SecurinestException(HttpStatus.CONFLICT, "Conflict creating policy");
    }
  }

  @Transactional
  public Page<CompliancePolicyResponse> list(UUID tenantId, PolicyState state, Pageable pageable) {
    log.debug("Listing compliance policies for tenant: {}, state: {}", tenantId, state);

    // Validate sort fields
    Pageable validatedPageable = PageableValidationUtils.validatePolicySort(pageable);

    Page<CompliancePolicy> policies;
    if (state != null) {
      policies = policyRepository.findAllByTenantIdAndState(tenantId, state, validatedPageable);
    } else {
      policies = policyRepository.findAllByTenantIdOrderByUpdatedAtDesc(tenantId,
          validatedPageable);
    }

    return policies.map(policyMapper::toResponse);
  }

  @Transactional
  public CompliancePolicyResponse get(UUID tenantId, UUID policyId, UUID requesterId) {
    if (tenantId == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing tenant ID");
    }
    if (policyId == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing policy ID");
    }
    if (requesterId == null) {
      throw new SecurinestException(HttpStatus.UNAUTHORIZED, "Missing current user");
    }

    log.debug("Getting compliance policy: {} for tenant: {}", policyId, tenantId);

    CompliancePolicy policy = policyRepository.findByIdAndTenantId(policyId, tenantId)
        .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Policy not found"));

    return policyMapper.toResponse(policy);
  }

  @Transactional
  public CompliancePolicyResponse update(UUID tenantId, UUID policyId,
      CompliancePolicyUpdateRequest request,
      Long ifMatchVersion, UUID requesterId, String requestId) {
    if (tenantId == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing tenant ID");
    }
    if (policyId == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing policy ID");
    }
    if (request == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing body");
    }
    if (requesterId == null) {
      throw new SecurinestException(HttpStatus.UNAUTHORIZED, "Missing current user");
    }

    log.debug("Updating compliance policy: {} for tenant: {}", policyId, tenantId);

    CompliancePolicy policy = policyRepository.findByIdAndTenantId(policyId, tenantId)
        .orElseThrow(() -> new ResponseStatusException(HttpStatus.NOT_FOUND, "Policy not found"));

    long expectedVersion = (ifMatchVersion != null) ? ifMatchVersion
        : ValidationUtils.defaultLong(request.version(), -1L);

    if (expectedVersion < 0) {
      throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "Missing version");
    }
    if (!Objects.equals(policy.getVersion(), expectedVersion)) {
      throw new SecurinestException(HttpStatus.CONFLICT, "Version mismatch");
    }

    boolean hasChanges = false;

    // Update name if provided
    if (StringUtils.hasText(request.name())) {
      String normalizedName = ValidationUtils.trimToNull(request.name());
      if (normalizedName != null && !normalizedName.equalsIgnoreCase(policy.getName())) {
        if (policyRepository.existsByTenantIdAndNameIgnoreCaseAndIdNot(tenantId, normalizedName,
            policyId)) {
          throw new SecurinestException(HttpStatus.CONFLICT,
              "Policy name already exists: " + normalizedName);
        }
        policy.setName(normalizedName);
        hasChanges = true;
      }
    }

    // Update description if provided
    if (request.description() != null) {
      String normalizedDescription = ValidationUtils.trimToNull(request.description());
      if (!Objects.equals(policy.getDescription(), normalizedDescription)) {
        policy.setDescription(normalizedDescription);
        hasChanges = true;
      }
    }

    // Update state if provided (only DRAFT <-> ACTIVE transitions allowed)
    if (StringUtils.hasText(request.state())) {
      PolicyState newState = PolicyState.valueOf(request.state());
      if (policy.getState() != newState) {
        if (isValidStateTransition(policy.getState(), newState)) {
          policy.setState(newState);
          hasChanges = true;
        } else {
          throw new SecurinestException(HttpStatus.BAD_REQUEST, "Invalid state transition from " +
              policy.getState() + " to " + newState);
        }
      }
    }

    // If no changes, return current state
    if (!hasChanges) {
      return policyMapper.toResponse(policy);
    }

    try {
      CompliancePolicy saved = policyRepository.save(policy);
      log.info("Updated compliance policy: {} for tenant: {} by user: {}", policyId, tenantId,
          requesterId);
      return policyMapper.toResponse(saved);
    } catch (ObjectOptimisticLockingFailureException e) {
      throw new SecurinestException(HttpStatus.CONFLICT, "Version mismatch");
    } catch (DataIntegrityViolationException e) {
      log.warn("updatePolicy constraint issue. requestId={} msg={}", requestId, e.getMessage());
      throw new SecurinestException(HttpStatus.CONFLICT, "Conflict updating policy");
    }
  }

  private boolean isValidStateTransition(PolicyState current, PolicyState target) {
    // P1: Only allow DRAFT <-> ACTIVE transitions
    return (current == PolicyState.DRAFT && target == PolicyState.ACTIVE) ||
        (current == PolicyState.ACTIVE && target == PolicyState.DRAFT);
  }
}
