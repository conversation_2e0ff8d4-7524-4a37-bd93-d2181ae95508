package io.securinest.controlplaneapi.service;

import io.securinest.controlplaneapi.dto.shared.PageResponse;
import io.securinest.controlplaneapi.dto.tenant.TenantMemberAddRequest;
import io.securinest.controlplaneapi.dto.tenant.TenantMemberResponse;
import io.securinest.controlplaneapi.dto.tenant.TenantMemberRoleUpdateRequest;
import io.securinest.controlplaneapi.entity.identity.UserAccount;
import io.securinest.controlplaneapi.entity.shared.Role;
import io.securinest.controlplaneapi.entity.tenant.Tenant;
import io.securinest.controlplaneapi.entity.tenant.TenantMember;
import io.securinest.controlplaneapi.exceptions.shared.SecurinestException;
import io.securinest.controlplaneapi.mapper.tenant.TenantMemberMapper;
import io.securinest.controlplaneapi.projection.tenant.TenantWithRole;
import io.securinest.controlplaneapi.repository.identity.UserAccountRepository;
import io.securinest.controlplaneapi.repository.tenant.TenantMemberRepository;
import io.securinest.controlplaneapi.repository.tenant.TenantRepository;
import io.securinest.controlplaneapi.util.shared.PageableValidationUtils;
import io.securinest.controlplaneapi.util.shared.ValidationUtils;
import io.securinest.controlplaneapi.util.tenant.TenantMemberUtils;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@RequiredArgsConstructor
@Slf4j
public class TenantMemberService {

  private final TenantRepository tenantRepository;
  private final TenantMemberRepository tenantMemberRepository;
  private final UserAccountRepository userAccountRepository;
  private final TenantMemberMapper tenantMemberMapper;

  public PageResponse<TenantMemberResponse> listMembers(UUID tenantId, UUID requesterUserId,
      Pageable pageable) {
    TenantWithRole tenantWithRole = tenantRepository.findTenantAndRole(tenantId, requesterUserId)
        .orElseThrow(() -> new SecurinestException(HttpStatus.NOT_FOUND, "Tenant not found"));

    Role requesterRole = tenantWithRole.role();

    if (!TenantMemberUtils.hasAtLeastViewer(requesterRole)) {
      throw new SecurinestException(HttpStatus.FORBIDDEN, "Insufficient role");
    }

    // Validate sort fields and pagination limits
    Pageable validatedPageable = PageableValidationUtils.validateTenantMemberSort(pageable);
    PageableValidationUtils.validatePaginationLimits(validatedPageable);

    Page<TenantMember> pagination = tenantMemberRepository.findAllByTenantId(tenantId,
        validatedPageable);

    Set<UUID> userIds = pagination.getContent().stream().map(TenantMember::getUserId)
        .collect(Collectors.toSet());

    Map<UUID, UserAccount> usersById = userAccountRepository.findAllById(userIds).stream()
        .collect(Collectors.toMap(UserAccount::getId, Function.identity()));

    boolean maskEmails = !TenantMemberUtils.isAdminOrOwner(requesterRole);

    List<TenantMemberResponse> records = pagination.getContent()
        .stream()
        .map(member -> tenantMemberMapper.toResponse(member, usersById.get(member.getUserId()),
            maskEmails))
        .toList();

    return new PageResponse<>(
        records,
        pagination.getNumber(),
        pagination.getSize(),
        pagination.getTotalElements(),
        pagination.getTotalPages()
    );
  }

  public long getTenantVersion(UUID tenantId, UUID requesterUserId) {
    TenantWithRole tenantWithRole = tenantRepository.findTenantAndRole(tenantId, requesterUserId)
        .orElseThrow(() -> new SecurinestException(HttpStatus.NOT_FOUND, "Tenant not found"));

    Role requesterRole = tenantWithRole.role();
    if (!TenantMemberUtils.hasAtLeastViewer(requesterRole)) {
      throw new SecurinestException(HttpStatus.FORBIDDEN, "Insufficient role");
    }

    return tenantWithRole.tenant().getVersion();
  }

  @Transactional
  public TenantMemberResponse addMember(UUID tenantId, UUID requesterUserId,
      TenantMemberAddRequest request, Long expectedVersion) {

    if (request == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing body");
    }

    if (tenantId == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing tenantId");
    }

    TenantWithRole tenantWithRole = tenantRepository.findTenantAndRole(tenantId, requesterUserId)
        .orElseThrow(() -> new SecurinestException(HttpStatus.NOT_FOUND, "Tenant not found"));

    Tenant tenant = tenantWithRole.tenant();
    Role requesterRole = tenantWithRole.role();

    if (!TenantMemberUtils.isAdminOrOwner(requesterRole)) {
      throw new SecurinestException(HttpStatus.FORBIDDEN, "Insufficient role");
    }

    if (expectedVersion != null && tenant.getVersion() != expectedVersion) {
      throw new SecurinestException(HttpStatus.PRECONDITION_FAILED, "Version mismatch");
    }

    UUID userId = ValidationUtils.parseUuid(request.userId(), "userId");
    Role role = TenantMemberUtils.parseRole(request.role());

    if (role == Role.OWNER) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Cannot assign OWNER via API");
    }

    UserAccount user = userAccountRepository.findById(userId)
        .orElseThrow(() -> new SecurinestException(HttpStatus.NOT_FOUND, "User not found"));

    Optional<TenantMember> existingMember = tenantMemberRepository.findByTenantIdAndUserId(tenantId,
        userId);

    if (existingMember.isPresent()) {
      TenantMember existing = existingMember.get();
      if (existing.getRole() == role) {
        boolean maskEmails = !TenantMemberUtils.isAdminOrOwner(requesterRole);
        return tenantMemberMapper.toResponse(existing, user, maskEmails);
      } else {
        throw new SecurinestException(HttpStatus.CONFLICT, "Member exists with different role");
      }
    }

    TenantMember member = new TenantMember();
    member.setTenantId(tenantId);
    member.setUserId(userId);
    member.setRole(role);

    try {
      TenantMember saved = tenantMemberRepository.save(member);

      tenant.setVersion(tenant.getVersion() + 1);
      tenantRepository.save(tenant);

      boolean maskEmails = !TenantMemberUtils.isAdminOrOwner(requesterRole);
      log.info("Member added. tenantId={} userId={} role={} requesterId={}", tenantId, userId,
          role, requesterUserId);
      return tenantMemberMapper.toResponse(saved, user, maskEmails);
    } catch (DataIntegrityViolationException e) {
      log.warn("addMember conflict. tenantId={} userId={} msg={}", tenantId, userId,
          e.getMessage());
      throw new SecurinestException(HttpStatus.CONFLICT, "Conflict creating membership");
    }
  }

  @Transactional
  public TenantMemberResponse updateMemberRole(UUID tenantId, UUID requesterUserId, UUID userId,
      TenantMemberRoleUpdateRequest req, Long expectedVersion) {

    if (req == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing body");
    }

    if (tenantId == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing tenantId");
    }

    TenantWithRole tenantWithRole = tenantRepository.findTenantAndRole(tenantId, requesterUserId)
        .orElseThrow(() -> new SecurinestException(HttpStatus.NOT_FOUND, "Tenant not found"));

    Tenant tenant = tenantWithRole.tenant();
    Role requesterRole = tenantWithRole.role();

    if (!TenantMemberUtils.isAdminOrOwner(requesterRole)) {
      throw new SecurinestException(HttpStatus.FORBIDDEN, "Insufficient role");
    }

    if (expectedVersion != null && tenant.getVersion() != expectedVersion) {
      throw new SecurinestException(HttpStatus.PRECONDITION_FAILED, "Version mismatch");
    }

    Role newRole = TenantMemberUtils.parseRole(req.role());
    TenantMember member = tenantMemberRepository.findByTenantIdAndUserId(tenantId, userId)
        .orElseThrow(() -> new SecurinestException(HttpStatus.NOT_FOUND, "Member not found"));

    Role currentRole = member.getRole();

    long ownerCount = tenantMemberRepository.countByTenantIdAndRole(tenantId, Role.OWNER);
    TenantMemberUtils.validateOwnerRoleChange(currentRole, newRole, requesterRole, ownerCount,
        requesterUserId, userId);

    member.setRole(newRole);
    TenantMember saved = tenantMemberRepository.save(member);

    tenant.setVersion(tenant.getVersion() + 1);
    tenantRepository.save(tenant);

    UserAccount user = userAccountRepository.findById(userId)
        .orElseThrow(() -> new SecurinestException(HttpStatus.NOT_FOUND, "User not found"));
    boolean maskEmails = !TenantMemberUtils.isAdminOrOwner(requesterRole);

    return tenantMemberMapper.toResponse(saved, user, maskEmails);
  }

  @Transactional
  public void removeMember(UUID tenantId, UUID requesterUserId, UUID userId, Long expectedVersion) {
    if (requesterUserId == null) {
      throw new SecurinestException(HttpStatus.UNAUTHORIZED, "Missing current user");
    }

    if (tenantId == null) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST, "Missing tenantId");
    }

    TenantWithRole tenantWithRole = tenantRepository.findTenantAndRole(tenantId, requesterUserId)
        .orElseThrow(() -> new SecurinestException(HttpStatus.NOT_FOUND, "Tenant not found"));

    Tenant tenant = tenantWithRole.tenant();
    Role requesterRole = tenantWithRole.role();

    if (!TenantMemberUtils.isAdminOrOwner(requesterRole)) {
      throw new SecurinestException(HttpStatus.FORBIDDEN, "Insufficient role");
    }

    if (expectedVersion != null && tenant.getVersion() != expectedVersion) {
      throw new SecurinestException(HttpStatus.PRECONDITION_FAILED, "Version mismatch");
    }

    TenantMember member = tenantMemberRepository.findByTenantIdAndUserId(tenantId, userId)
        .orElseThrow(() -> new SecurinestException(HttpStatus.NOT_FOUND, "Member not found"));

    long ownerCount = tenantMemberRepository.countByTenantIdAndRole(tenantId, Role.OWNER);
    TenantMemberUtils.validateOwnerRemoval(member.getRole(), requesterRole, ownerCount,
        requesterUserId, userId);

    tenantMemberRepository.delete(member);

    tenant.setVersion(tenant.getVersion() + 1);
    tenantRepository.save(tenant);
  }
}

