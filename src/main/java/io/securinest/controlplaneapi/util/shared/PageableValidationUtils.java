package io.securinest.controlplaneapi.util.shared;

import io.securinest.controlplaneapi.exceptions.shared.SecurinestException;
import java.util.Set;
import lombok.experimental.UtilityClass;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.HttpStatus;

/**
 * Utility class for validating Pageable objects and their sort fields
 */
@UtilityClass
public class PageableValidationUtils {

  // Common sort field sets for different entities
  public static final Set<String> AUDIT_LOG_SORT_FIELDS = Set.of("ts");
  public static final Set<String> AUDIT_PACK_SORT_FIELDS = Set.of("createdAt");
  public static final Set<String> TENANT_SORT_FIELDS = Set.of("name", "slug", "createdAt",
      "updatedAt");
  public static final Set<String> TENANT_MEMBER_SORT_FIELDS = Set.of("role", "joinedAt");
  public static final Set<String> POLICY_SORT_FIELDS = Set.of("name", "state", "createdAt",
      "updatedAt");
  public static final Set<String> POLICY_VERSION_SORT_FIELDS = Set.of("versionNo", "createdAt");
  public static final Set<String> POLICY_BUNDLE_SORT_FIELDS = Set.of("createdAt", "updatedAt");

  /**
   * Validates sort fields in Pageable against allowed fields for audit logs
   */
  public static Pageable validateAuditLogSort(Pageable pageable) {
    return validateSort(pageable, AUDIT_LOG_SORT_FIELDS);
  }

  /**
   * Validates sort fields in Pageable against allowed fields for audit packs
   */
  public static Pageable validateAuditPackSort(Pageable pageable) {
    return validateSort(pageable, AUDIT_PACK_SORT_FIELDS);
  }

  /**
   * Validates sort fields in Pageable against allowed fields for tenants
   */
  public static Pageable validateTenantSort(Pageable pageable) {
    return validateSort(pageable, TENANT_SORT_FIELDS);
  }

  /**
   * Validates sort fields in Pageable against allowed fields for tenant members
   */
  public static Pageable validateTenantMemberSort(Pageable pageable) {
    return validateSort(pageable, TENANT_MEMBER_SORT_FIELDS);
  }

  /**
   * Validates sort fields in Pageable against allowed fields for policies
   */
  public static Pageable validatePolicySort(Pageable pageable) {
    return validateSort(pageable, POLICY_SORT_FIELDS);
  }

  /**
   * Validates sort fields in Pageable against allowed fields for policy versions
   */
  public static Pageable validatePolicyVersionSort(Pageable pageable) {
    return validateSort(pageable, POLICY_VERSION_SORT_FIELDS);
  }

  /**
   * Validates sort fields in Pageable against allowed fields for policy bundles
   */
  public static Pageable validatePolicyBundleSort(Pageable pageable) {
    return validateSort(pageable, POLICY_BUNDLE_SORT_FIELDS);
  }

  /**
   * Generic method to validate sort fields in Pageable against a custom set of allowed fields
   */
  public static Pageable validateSort(Pageable pageable, Set<String> allowedFields) {
    if (pageable.getSort().isEmpty()) {
      return pageable;
    }

    // Validate each sort field
    for (Sort.Order order : pageable.getSort()) {
      String field = order.getProperty();
      if (!allowedFields.contains(field)) {
        throw new SecurinestException(HttpStatus.BAD_REQUEST,
            "Invalid sort field: " + field + ". Allowed: " + String.join(", ", allowedFields));
      }
    }

    return pageable;
  }

  /**
   * Validates pagination parameters and applies size limits
   */
  public static void validatePaginationLimits(Pageable pageable) {
    validatePaginationLimits(pageable, Constants.PAGE_MAX_SIZE);
  }

  /**
   * Validates pagination parameters with custom size limit
   */
  public static void validatePaginationLimits(Pageable pageable, int maxSize) {
    if (pageable.getPageSize() > maxSize) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST,
          "Page size too large. Maximum allowed: " + maxSize);
    }

    if (pageable.getPageNumber() < 0) {
      throw new SecurinestException(HttpStatus.BAD_REQUEST,
          "Page number must be non-negative");
    }
  }
}
