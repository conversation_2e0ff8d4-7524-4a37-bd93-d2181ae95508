package io.securinest.controlplaneapi.mapper.tenant;

import io.securinest.controlplaneapi.dto.identity.UserAccountResponse;
import io.securinest.controlplaneapi.dto.tenant.TenantMemberResponse;
import io.securinest.controlplaneapi.entity.identity.UserAccount;
import io.securinest.controlplaneapi.entity.tenant.TenantMember;
import io.securinest.controlplaneapi.mapper.MapperValidationUtils;
import java.util.UUID;
import org.mapstruct.Context;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = "spring",
    injectionStrategy = InjectionStrategy.CONSTRUCTOR,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface TenantMemberMapper {

  @Mapping(target = "tenantId", expression = "java(MapperValidationUtils.uuidToStringOrNull(member.getTenantId()))")
  @Mapping(target = "userId", expression = "java(MapperValidationUtils.uuidToStringOrNull(member.getUserId()))")
  @Mapping(target = "role", expression = "java(MapperValidationUtils.enumNameOrNull(member.getRole()))")
  @Mapping(target = "createdAt", source = "member.createdAt")
  @Mapping(target = "user", expression = "java(toUser(user, maskEmails))")
  TenantMemberResponse toResponse(TenantMember member, UserAccount user,
      @Context boolean maskEmails);

  default UserAccountResponse toUser(UserAccount u, @Context boolean maskEmails) {
    if (u == null) {
      return null;
    }
    String email = u.getEmail();
    if (maskEmails && email != null) {
      email = maskEmail(email);
    }
    return UserAccountResponse.builder()
        .id(MapperValidationUtils.uuidToStringOrNull(u.getId()))
        .kcSub(u.getKcSub())
        .email(email)
        .displayName(u.getDisplayName())
        .createdAt(u.getCreatedAt())
        .updatedAt(u.getUpdatedAt())
        .version(u.getVersion())
        .build();
  }



  default String maskEmail(String email) {
    int at = email.indexOf('@');
    if (at <= 1) {
      return "***" + email.substring(Math.max(at, 0));
    }
    String name = email.substring(0, at);
    String domain = email.substring(at);
    String visible = name.substring(0, 1);
    return visible + "***" + domain;
  }
}

