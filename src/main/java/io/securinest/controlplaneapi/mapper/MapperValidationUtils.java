package io.securinest.controlplaneapi.mapper;

import java.util.UUID;

/**
 * Utility class containing common validation and conversion methods for MapStruct mappers.
 * This class provides null-safe conversion methods that can be used across all mappers
 * to ensure consistent handling of null values and type conversions.
 */
public final class MapperValidationUtils {

  private MapperValidationUtils() {
    // Utility class - prevent instantiation
  }

  /**
   * Converts an object to its string representation, or null if the object is null.
   * This method provides null-safe string conversion for mapping operations.
   *
   * @param obj the object to convert to string
   * @return the string representation of the object, or null if the object is null
   */
  public static String toStringOrNull(Object obj) {
    return obj != null ? obj.toString() : null;
  }

  /**
   * Gets the name of an enum constant, or null if the enum is null.
   * This method provides null-safe enum name extraction for mapping operations.
   *
   * @param enumValue the enum value to get the name from
   * @return the name of the enum constant, or null if the enum is null
   */
  public static String enumNameOrNull(Enum<?> enumValue) {
    return enumValue != null ? enumValue.name() : null;
  }

  /**
   * Converts a UUID to its string representation, or null if the UUID is null.
   * This method provides null-safe UUID to string conversion for mapping operations.
   *
   * @param uuid the UUID to convert to string
   * @return the string representation of the UUID, or null if the UUID is null
   */
  public static String uuidToStringOrNull(UUID uuid) {
    return uuid != null ? uuid.toString() : null;
  }
}
