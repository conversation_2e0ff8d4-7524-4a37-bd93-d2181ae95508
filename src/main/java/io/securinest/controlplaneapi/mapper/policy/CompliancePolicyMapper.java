package io.securinest.controlplaneapi.mapper.policy;

import io.securinest.controlplaneapi.dto.policy.CompliancePolicyCreateRequest;
import io.securinest.controlplaneapi.dto.policy.CompliancePolicyResponse;
import io.securinest.controlplaneapi.entity.policy.CompliancePolicy;
import io.securinest.controlplaneapi.mapper.MapperValidationUtils;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = "spring",
    injectionStrategy = InjectionStrategy.CONSTRUCTOR,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface CompliancePolicyMapper {

  @Mapping(target = "state", expression = "java(MapperValidationUtils.enumNameOrNull(entity.getState()))")
  CompliancePolicyResponse toResponse(CompliancePolicy entity);

  @Mapping(target = "id", ignore = true)
  @Mapping(target = "tenantId", ignore = true)
  @Mapping(target = "state", expression = "java(io.securinest.controlplaneapi.entity.shared.PolicyState.DRAFT)")
  @Mapping(target = "createdBy", ignore = true)
  @Mapping(target = "createdAt", ignore = true)
  @Mapping(target = "updatedAt", ignore = true)
  @Mapping(target = "version", ignore = true)
  CompliancePolicy fromRequest(CompliancePolicyCreateRequest request);

}
