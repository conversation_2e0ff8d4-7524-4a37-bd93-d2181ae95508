package io.securinest.controlplaneapi.mapper.audit;

import io.securinest.controlplaneapi.dto.audit.AuditLogEntryResponse;
import io.securinest.controlplaneapi.dto.audit.AuditPackResponse;
import io.securinest.controlplaneapi.dto.audit.AuditPackScopeResponse;
import io.securinest.controlplaneapi.dto.shared.TimeRangeResponse;
import io.securinest.controlplaneapi.entity.audit.AuditLogEntry;
import io.securinest.controlplaneapi.entity.audit.AuditPack;
import java.util.List;
import java.util.Map;
import org.mapstruct.InjectionStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;

@Mapper(
    componentModel = "spring",
    injectionStrategy = InjectionStrategy.CONSTRUCTOR,
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface AuditMapper {

  @Mapping(target = "id", expression = "java(toStringOrNull(entity.getId()))")
  @Mapping(target = "tenantId", expression = "java(toStringOrNull(entity.getTenantId()))")
  @Mapping(target = "actorUserId", expression = "java(toStringOrNull(entity.getActorUserId()))")
  @Mapping(target = "action", expression = "java(enumNameOrNull(entity.getAction()))")
  @Mapping(target = "targetId", expression = "java(toStringOrNull(entity.getTargetId()))")
  AuditLogEntryResponse toResponse(AuditLogEntry entity);

  @Mapping(target = "id", expression = "java(toStringOrNull(entity.getId()))")
  @Mapping(target = "tenantId", expression = "java(toStringOrNull(entity.getTenantId()))")
  @Mapping(target = "status", expression = "java(enumNameOrNull(entity.getStatus()))")
  @Mapping(target = "createdBy", expression = "java(toStringOrNull(entity.getCreatedBy()))")
  @Mapping(target = "scope", expression = "java(mapScope(entity.getScope()))")
  AuditPackResponse toResponse(AuditPack entity);

  /**
   * Converts an object to its string representation, or null if the object is null. This method
   * provides null-safe string conversion for mapping operations.
   *
   * @param obj the object to convert to string
   * @return the string representation of the object, or null if the object is null
   */
  default String toStringOrNull(Object obj) {
    return obj != null ? obj.toString() : null;
  }

  /**
   * Gets the name of an enum constant, or null if the enum is null. This method provides null-safe
   * enum name extraction for mapping operations.
   *
   * @param enumValue the enum value to get the name from
   * @return the name of the enum constant, or null if the enum is null
   */
  default String enumNameOrNull(Enum<?> enumValue) {
    return enumValue != null ? enumValue.name() : null;
  }

  /**
   * Maps the scope JSON to AuditPackScopeResponse
   */
  default AuditPackScopeResponse mapScope(Map<String, Object> scope) {
    if (scope == null) {
      return null;
    }

    @SuppressWarnings("unchecked")
    List<String> services = (List<String>) scope.get("services");

    @SuppressWarnings("unchecked")
    List<String> envs = (List<String>) scope.get("envs");

    @SuppressWarnings("unchecked")
    Map<String, Object> dateRangeMap = (Map<String, Object>) scope.get("dateRange");

    TimeRangeResponse dateRange = null;
    if (dateRangeMap != null) {
      String from = (String) dateRangeMap.get("from");
      String to = (String) dateRangeMap.get("to");
      dateRange = TimeRangeResponse.builder()
          .from(from != null ? java.time.Instant.parse(from) : null)
          .to(to != null ? java.time.Instant.parse(to) : null)
          .build();
    }

    return AuditPackScopeResponse.builder()
        .services(services)
        .envs(envs)
        .dateRange(dateRange)
        .build();
  }
}
