package io.securinest.controlplaneapi.controller.audit;

import io.securinest.controlplaneapi.dto.audit.AuditLogEntryResponse;
import io.securinest.controlplaneapi.dto.audit.AuditPackRequest;
import io.securinest.controlplaneapi.dto.audit.AuditPackResponse;
import io.securinest.controlplaneapi.dto.shared.PageResponse;
import io.securinest.controlplaneapi.service.AuditService;
import io.securinest.controlplaneapi.util.shared.ResponseEntityUtils;
import jakarta.validation.Valid;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/tenants/{tenantId}/audit")
public class AuditController {

  private final AuditService auditService;

  @GetMapping("/logs")
  public ResponseEntity<PageResponse<AuditLogEntryResponse>> getAuditLogs(
      @PathVariable UUID tenantId,
      @RequestParam(required = false) String action,
      @RequestParam(required = false) String targetType,
      @RequestParam(required = false) String targetId,
      @RequestParam(required = false) String actorUserId,
      @RequestParam(required = false) String from,
      @RequestParam(required = false) String to,
      @PageableDefault(size = 20) Pageable pageable,
      @RequestHeader(name = "X-Debug-UserId") UUID debugUserId) {

    PageResponse<AuditLogEntryResponse> response = auditService.getAuditLogs(
        tenantId,
        debugUserId,
        action,
        targetType,
        targetId,
        actorUserId,
        from,
        to,
        pageable
    );

    return ResponseEntityUtils.ok(response);
  }

  @PostMapping("/packs")
  public ResponseEntity<AuditPackResponse> createAuditPack(
      @PathVariable UUID tenantId,
      @Valid @RequestBody AuditPackRequest request,
      @RequestHeader(name = "X-Debug-UserId") UUID debugUserId,
      @RequestHeader(name = "X-Request-Id", required = false) String requestId,
      @RequestHeader(name = "Idempotency-Key", required = false) String idempotencyKey) {

    AuditPackResponse response = auditService.createAuditPack(
        tenantId,
        debugUserId,
        request,
        idempotencyKey,
        requestId
    );

    return ResponseEntityUtils.created(response);
  }

  @GetMapping("/packs")
  public ResponseEntity<PageResponse<AuditPackResponse>> listAuditPacks(
      @PathVariable UUID tenantId,
      @RequestParam(required = false) String status,
      @RequestParam(required = false) String from,
      @RequestParam(required = false) String to,
      @PageableDefault(size = 20) Pageable pageable,
      @RequestHeader(name = "X-Debug-UserId") UUID debugUserId) {

    PageResponse<AuditPackResponse> response = auditService.listAuditPacks(
        tenantId,
        debugUserId,
        status,
        from,
        to,
        pageable
    );

    return ResponseEntityUtils.ok(response);
  }

  @GetMapping("/packs/{packId}")
  public ResponseEntity<AuditPackResponse> getAuditPack(
      @PathVariable UUID tenantId,
      @PathVariable UUID packId,
      @RequestHeader(name = "X-Debug-UserId") UUID debugUserId) {

    AuditPackResponse response = auditService.getAuditPack(tenantId, packId, debugUserId);

    return ResponseEntityUtils.ok(response);
  }
}
