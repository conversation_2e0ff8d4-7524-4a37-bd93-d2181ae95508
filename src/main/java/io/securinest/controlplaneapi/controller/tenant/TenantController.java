package io.securinest.controlplaneapi.controller.tenant;

import io.securinest.controlplaneapi.dto.shared.PageResponse;
import io.securinest.controlplaneapi.dto.tenant.TenantCreateRequest;
import io.securinest.controlplaneapi.dto.tenant.TenantResponse;
import io.securinest.controlplaneapi.dto.tenant.TenantUpdateRequest;
import io.securinest.controlplaneapi.service.TenantService;
import io.securinest.controlplaneapi.util.shared.ResponseEntityUtils;
import io.securinest.controlplaneapi.util.shared.ValidationUtils;
import jakarta.validation.Valid;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/tenants", produces = "application/json")
public class TenantController {

  private final TenantService tenantService;

  @PostMapping
  public ResponseEntity<TenantResponse> createTenant(
      @RequestHeader(name = "X-Debug-UserId") UUID debugUserId,
      @RequestHeader(name = "X-Request-Id", required = false) String requestId,
      @RequestHeader(name = "X-Idempotency-Key", required = false) String idempotencyKey,
      @RequestBody @Valid TenantCreateRequest body
  ) {
    TenantResponse res = tenantService.createTenant(debugUserId, body, requestId);
    return ResponseEntityUtils.createResponseWithVersionedResource(res);
  }

  @GetMapping
  public ResponseEntity<PageResponse<TenantResponse>> listMyTenants(
      @RequestHeader(name = "X-Debug-UserId") UUID debugUserId,
      @PageableDefault(size = 20) Pageable pageable
  ) {
    PageResponse<TenantResponse> body = tenantService.listTenantsForUser(debugUserId, pageable);

    return ResponseEntity.ok()
        .header(HttpHeaders.CACHE_CONTROL, "no-store")
        .body(body);
  }

  @GetMapping(path = "/{tenantId}")
  public ResponseEntity<TenantResponse> getTenant(
      @PathVariable UUID tenantId,
      @RequestHeader(name = "X-Debug-UserId") UUID debugUserId
  ) {
    TenantResponse res = tenantService.getTenant(tenantId, debugUserId);
    return ResponseEntityUtils.getResponseWithVersionedResource(res);
  }

  @PutMapping(path = "/{tenantId}")
  public ResponseEntity<TenantResponse> updateTenant(
      @PathVariable UUID tenantId,
      @RequestHeader(name = "X-Debug-UserId") UUID debugUserId,
      @RequestHeader(name = "If-Match", required = false) String ifMatch,
      @RequestHeader(name = "X-Request-Id", required = false) String requestId,
      @RequestBody @Valid TenantUpdateRequest body
  ) {
    if (ifMatch == null) {
      return ResponseEntityUtils.preconditionRequired(TenantResponse.class);
    }

    Long expectedVersion = ValidationUtils.parseIfMatch(ifMatch);
    TenantResponse res = tenantService.updateTenant(tenantId, debugUserId, body, expectedVersion,
        requestId);

    return ResponseEntityUtils.updateResponseWithVersionedResource(res);
  }
}
