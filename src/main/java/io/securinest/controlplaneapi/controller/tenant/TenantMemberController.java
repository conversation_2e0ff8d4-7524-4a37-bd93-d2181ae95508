package io.securinest.controlplaneapi.controller.tenant;

import io.securinest.controlplaneapi.dto.shared.PageResponse;
import io.securinest.controlplaneapi.dto.tenant.TenantMemberAddRequest;
import io.securinest.controlplaneapi.dto.tenant.TenantMemberResponse;
import io.securinest.controlplaneapi.dto.tenant.TenantMemberRoleUpdateRequest;
import io.securinest.controlplaneapi.service.TenantMemberService;
import io.securinest.controlplaneapi.util.shared.ResponseEntityUtils;
import io.securinest.controlplaneapi.util.shared.ValidationUtils;
import jakarta.validation.Valid;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PatchMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping(path = "/tenants/{tenantId}/members", produces = "application/json")
public class TenantMemberController {

  private final TenantMemberService tenantMemberService;

  @GetMapping
  public ResponseEntity<PageResponse<TenantMemberResponse>> listMembers(
      @PathVariable UUID tenantId,
      @RequestHeader(name = "X-Debug-UserId") UUID debugUserId,
      @PageableDefault(size = 20) Pageable pageable
  ) {
    PageResponse<TenantMemberResponse> res = tenantMemberService.listMembers(tenantId, debugUserId,
        pageable);
    long tenantVersion = tenantMemberService.getTenantVersion(tenantId, debugUserId);

    return ResponseEntityUtils.getResponseWithVersionedResource(res, tenantVersion);
  }

  @PostMapping(consumes = "application/json")
  public ResponseEntity<TenantMemberResponse> addMember(
      @PathVariable UUID tenantId,
      @RequestHeader(name = "X-Debug-UserId") UUID debugUserId,
      @RequestHeader(name = "If-Match", required = false) String ifMatch,
      @RequestBody @Valid TenantMemberAddRequest body
  ) {
    if (ifMatch == null) {
      return ResponseEntityUtils.preconditionRequired(TenantMemberResponse.class);
    }

    Long expectedVersion = ValidationUtils.parseIfMatch(ifMatch);
    TenantMemberResponse res = tenantMemberService.addMember(tenantId, debugUserId, body,
        expectedVersion);
    long tenantVersion = tenantMemberService.getTenantVersion(tenantId, debugUserId);

    return ResponseEntityUtils.createResponseWithVersionedResource(res, res.userId(), "/{userId}",
        tenantVersion);
  }

  @PatchMapping(path = "/{userId}", consumes = "application/json")
  public ResponseEntity<TenantMemberResponse> updateMemberRole(
      @PathVariable UUID tenantId,
      @PathVariable UUID userId,
      @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId,
      @RequestHeader(name = "If-Match", required = false) String ifMatch,
      @RequestBody @Valid TenantMemberRoleUpdateRequest body
  ) {
    if (debugUserId == null) {
      return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
    }

    if (ifMatch == null) {
      return ResponseEntityUtils.preconditionRequired(TenantMemberResponse.class);
    }

    Long expectedVersion = ValidationUtils.parseIfMatch(ifMatch);
    TenantMemberResponse res = tenantMemberService.updateMemberRole(tenantId, debugUserId, userId,
        body, expectedVersion);
    long tenantVersion = tenantMemberService.getTenantVersion(tenantId, debugUserId);

    return ResponseEntityUtils.updateResponseWithVersionedResource(res, tenantVersion);
  }

  @DeleteMapping(path = "/{userId}")
  public ResponseEntity<Void> removeMember(
      @PathVariable UUID tenantId,
      @PathVariable UUID userId,
      @RequestHeader(name = "X-Debug-UserId") UUID debugUserId,
      @RequestHeader(name = "If-Match", required = false) String ifMatch
  ) {
    if (ifMatch == null) {
      return ResponseEntityUtils.preconditionRequired(Void.class);
    }

    Long expectedVersion = ValidationUtils.parseIfMatch(ifMatch);
    tenantMemberService.removeMember(tenantId, debugUserId, userId, expectedVersion);

    return ResponseEntityUtils.deleteNoContent();
  }
}
